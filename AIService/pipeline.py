import json
from services.proposal.utilities import ProposalUtilities
from controllers.customer.tenant_controller import <PERSON>ant<PERSON><PERSON>roller
from services.proposal.technical_requirements import TechnicalRequirementsService
from services.proposal.content_compliance import ContentComplianceService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db
from services.proposal.rfp.rfp_section import RFPSection
from services.data_load.process_document import DocumentProcessingService
from services.chroma.chroma_service import ChromaService
import asyncio

# Adjust these as needed for your environment
PROCESSED_TEXT_PATH = "AIService/processed_text.txt"
COLLECTION_NAME = "test_collection"  # Set this to your target collection
EMBEDDING_API_URL = "http://ai.kontratar.com:5000"  # Set as needed

# If you need a DB session, import your session factory
# from database import async_session_factory

async def main():
    import logging

    logger = logging.getLogger("pipeline")
    logging.basicConfig(level=logging.INFO)

    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    client = "adeptengineeringsolutions"
    is_rfp = True

    logger.info(f"Starting pipeline for opportunity_id={opportunity_id}, tenant_id={tenant_id}, client={client}")

    async for db in get_customer_db():
        logger.info("Fetching opportunity record from database...")
        record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)

        logger.info("Fetching tenant info from database...")
        tenant_info = await TenantController.get_by_tenant_id(db, tenant_id)
        
        break

    if record is None or tenant_info is None:
        logger.error("Record or tenant_info is None. Exiting pipeline.")
        return None

    logger.info("Loading table_of_contents and outline from record...")
    table_of_contents = str(record.toc_text)
    table_of_contents = json.loads(table_of_contents)

    outline = str(record.proposal_outline_1)
    outline = json.loads(outline).get("outlines")

    logger.info(f"Outline: {outline}")

    opportunity_metadata = json.dumps(record.as_dict())
    tenant_metadata = json.dumps(tenant_info.as_dict())

    tech_service = TechnicalRequirementsService()
    content_service = ContentComplianceService()

    technical_requirements = str(record.requirement_text)
    content_compliance = str(record.content_compliance)

    if not technical_requirements:
        logger.info("Technical requirements not found. Generating Technical Requirements...")
        tech_result = await tech_service.generate_technical_requirements(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source
        )
        logger.info("--- Technical Requirements generated ---")
        technical_requirements = tech_result["content"]

    if not content_compliance:
        logger.info("Content compliance not found. Generating Content Compliance...")
        content_result = await content_service.generate_content_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            is_rfp=is_rfp,
        )
        logger.info("--- Content Compliance generated ---")
        content_compliance = content_result["content"]

    proposal = []

    logger.info("Beginning proposal section generation...")
    for idx, item in enumerate(table_of_contents):
        logger.info(f"Processing section {idx+1}/{len(table_of_contents)}: {item.get('title', '')}")
        outline_guide = outline[idx].get("content", "")
        rfp_section_generator = RFPSection(
            outline_guide = outline_guide,
            technical_requirements = technical_requirements,
            content_compliance=content_compliance,
            tenant_metadata=tenant_metadata,
            opportunity_metadata=opportunity_metadata,
            tenant_id=tenant_id,
            client=client,
            profile_id=None,
            source_documents=[],
            internet_search_results=False
        )

        title = item.get("title", "")
        description = item.get("description", "")
        subsections = item.get("subsections", [])

        logger.info(f"Generating RFP section header for '{title}'")
        header = await rfp_section_generator.generate_rfp_section_header(title, description, outline_guide)
        header = ProposalUtilities.extract_json_from_brackets(header)

        if header is None:
            logger.error(f"Failed to generate header for section '{title}'. Exiting pipeline.")
            return None

        header["subsections"] = []

        for i, section in enumerate(subsections):
            sub_title = section.get("title", "")
            sub_description = section.get("description", "")

            try:
                outline_guide = outline[idx].get("subsections", [])[i]
            except (IndexError, KeyError, TypeError) as e:
                logger.warning(f"Error retrieving outline guide for subsection {i} of section {idx}: {e}")
                outline_guide = ""

            logger.info(f"Generating RFP subsection for '{sub_title}'")
            subsection = await rfp_section_generator.generate_rfp_subsection(sub_title, sub_description, outline_guide)
            subsection = ProposalUtilities.extract_json_from_brackets(subsection)
            header["subsections"].append(subsection)

        proposal.append(header)

    print(proposal)

async def test():
    string = """
    ```json
    [
        "US Federal Government subcontracting disclosure requirements",
        "Federal government teaming agreement documentation standards"
    ]
    ```
    """

    array = ProposalUtilities.extract_json_from_brackets(string, "[]")
    print(array)

if __name__ == "__main__":
    asyncio.run(test())