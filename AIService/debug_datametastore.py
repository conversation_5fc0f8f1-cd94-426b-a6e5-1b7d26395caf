import asyncio
import sys
import os
from datetime import datetime

# THIS is a temporary debug file would be removed later
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from loguru import logger

# Import the controller and models
from AIService.controllers.customer.datametastore_controller import DataMetastoreController
from AIService.database import get_customer_db
from AIService.services.scheduler_service.datametastore_scheduler_service import DataMetastoreSchedulerService

datametastore_controller = DataMetastoreSchedulerService()
 
async def debug_datametastore():
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO")
   
    
    async for session in get_customer_db():
        try:
            # Directly call the method with a specific datetime
            logger.info("Calling get_partner_doc_by_date method...")
            result = await datametastore_controller.process_datametastore_partner_docs()
            
            # Print results
            if result:
                logger.info(f"Found {len(result)} records")
                for record in result:
                    logger.info(f"Record: {record.__dict__}")
            else:
                logger.warning("No records found")
        
        except Exception as e:
            logger.error(f"Error in debug_datametastore: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await session.close()

if __name__ == '__main__':
    asyncio.run(debug_datametastore()) 