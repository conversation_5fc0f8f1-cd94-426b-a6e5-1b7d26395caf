from datetime import datetime
from typing import Any, Dict, List, Optional

import uvicorn
from config import settings
from database import close_db, get_customer_db, init_db
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from pydantic import BaseModel
from services.queue_service.custom_opps_queue_service import \
    CustomOppsQueueService
from services.queue_service.proposal_queue_service import ProposalQueueService
from services.scheduler_service.scheduler_manager import SchedulerManager
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController
from sqlalchemy.ext.asyncio import AsyncSession
from category_generator import main as category_main  
import asyncio



# Pydantic models for API requests/responses
class ProposalQueueCreate(BaseModel):
    job_instruction: str
    opps_id: str
    tenant_id: str
    request_type: int
    job_submitted_by: Optional[str] = None
    next_state: Optional[str] = None

class CustomOppsQueueCreate(BaseModel):
    opps_source: str
    opps_id: str
    tenant_id: str
    originating_ip_address: Optional[str] = None

class QueueStatusUpdate(BaseModel):
    status: str
    next_state: Optional[str] = None

class SchedulerConfig(BaseModel):
    interval_seconds: int = 30

# Create FastAPI app
app = FastAPI(
    title="AI Service",
    description="AI Service for processing queue items",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize scheduler manager
scheduler_manager = SchedulerManager()

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize database and start schedulers on startup"""
    try:
        await init_db()
        logger.info("Database initialized successfully")
        
        # Start all schedulers
        scheduler_manager.start_all(settings.scheduler_interval_seconds)
        logger.info("All schedulers started successfully")
        
        # Enable all schedulers if env variable is set
        if settings.scheduler_enable_on_startup:
            scheduler_manager.enable_all()
            logger.info("All schedulers enabled on startup (via env variable)")
        else:
            logger.info("Schedulers are disabled on startup (via env variable)")
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    try:
        scheduler_manager.stop_all()
        await close_db()
        logger.info("Application shutdown completed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "scheduler_running": scheduler_manager.is_running,
        "scheduler_enabled": scheduler_manager.is_enabled
    }

# Scheduler management endpoints
@app.get("/scheduler/status")
async def get_scheduler_status():
    """Get comprehensive scheduler status"""
    return scheduler_manager.get_status()

@app.get("/scheduler/proposal/status")
async def get_proposal_scheduler_status():
    """Get proposal scheduler status"""
    return scheduler_manager.get_proposal_scheduler_status()

@app.get("/scheduler/custom-opps/status")
async def get_custom_opps_scheduler_status():
    """Get custom opps scheduler status"""
    return scheduler_manager.get_custom_opps_scheduler_status()

@app.post("/scheduler/start")
async def start_scheduler(config: SchedulerConfig):
    """Start all schedulers with specified interval"""
    try:
        scheduler_manager.start_all(config.interval_seconds)
        return {"message": f"All schedulers started with {config.interval_seconds} second interval"}
    except Exception as e:
        logger.error(f"Error starting schedulers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/stop")
async def stop_scheduler():
    """Stop all schedulers"""
    try:
        scheduler_manager.stop_all()
        return {"message": "All schedulers stopped"}
    except Exception as e:
        logger.error(f"Error stopping schedulers: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/scheduler/simulation/start")
async def start_simulation_scheduler(config: SchedulerConfig = SchedulerConfig(interval_seconds=120)):
    """
    Start the simulation scheduler with the specified interval (default: 120 seconds).
    """
    try:
        scheduler_manager.simulation_scheduler.start(config.interval_seconds)
        return {"message": f"Simulation scheduler started with {config.interval_seconds} second interval"}
    except Exception as e:
        logger.error(f"Error starting simulation scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/restart")
async def restart_scheduler(config: SchedulerConfig):
    """Restart all schedulers with specified interval"""
    try:
        scheduler_manager.restart_all(config.interval_seconds)
        return {"message": f"All schedulers restarted with {config.interval_seconds} second interval"}
    except Exception as e:
        logger.error(f"Error restarting schedulers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/enable")
async def enable_scheduler():
    """Enable all schedulers (allows jobs to run)"""
    try:
        scheduler_manager.enable_all()
        return {"message": "All schedulers enabled"}
    except Exception as e:
        logger.error(f"Error enabling schedulers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/disable")
async def disable_scheduler():
    """Disable all schedulers (prevents jobs from running)"""
    try:
        scheduler_manager.disable_all()
        return {"message": "All schedulers disabled"}
    except Exception as e:
        logger.error(f"Error disabling schedulers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Individual scheduler control endpoints
@app.post("/scheduler/proposal/enable")
async def enable_proposal_scheduler():
    """Enable only the proposal scheduler"""
    try:
        scheduler_manager.enable_proposal_scheduler()
        return {"message": "Proposal scheduler enabled"}
    except Exception as e:
        logger.error(f"Error enabling proposal scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/proposal/disable")
async def disable_proposal_scheduler():
    """Disable only the proposal scheduler"""
    try:
        scheduler_manager.disable_proposal_scheduler()
        return {"message": "Proposal scheduler disabled"}
    except Exception as e:
        logger.error(f"Error disabling proposal scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/custom-opps/enable")
async def enable_custom_opps_scheduler():
    """Enable only the custom opps scheduler"""
    try:
        scheduler_manager.enable_custom_opps_scheduler()
        return {"message": "Custom opps scheduler enabled"}
    except Exception as e:
        logger.error(f"Error enabling custom opps scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scheduler/custom-opps/disable")
async def disable_custom_opps_scheduler():
    """Disable only the custom opps scheduler"""
    try:
        scheduler_manager.disable_custom_opps_scheduler()
        return {"message": "Custom opps scheduler disabled"}
    except Exception as e:
        logger.error(f"Error disabling custom opps scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))
#simulation
@app.post("/scheduler/simulation/enable")
async def enable_simulation_scheduler():
    """Enable only the simulation scheduler"""
    try:
        scheduler_manager.simulation_scheduler.enable()
        return {"message": "Simulation scheduler enabled"}
    except Exception as e:
        logger.error(f"Error enabling simulation scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Proposal Queue endpoints
@app.get("/queue/proposal/new")
async def get_new_proposal_queue_items(
    limit: int = 10,
    db: AsyncSession = Depends(get_customer_db)
):
    """Get new proposal queue items"""
    try:
        items = await ProposalQueueService.get_proposal_queue_items(db, limit)
        return {
            "items": [
                {
                    "id": item.id,
                    "job_id": item.job_id,
                    "job_instruction": item.job_instruction,
                    "status": item.status,
                    "opps_id": item.opps_id,
                    "tenant_id": item.tenant_id,
                    "request_type": item.request_type,
                    "creation_date": item.creation_date.isoformat() if item.creation_date else None
                }
                for item in items
            ],
            "count": len(items)
        }
    except Exception as e:
        logger.error(f"Error getting new proposal queue items: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/queue/proposal")
async def create_proposal_queue_item(
    item: ProposalQueueCreate,
    db: AsyncSession = Depends(get_customer_db)
):
    """Create a new proposal queue item"""
    try:
        new_item = await ProposalQueueService.create_proposal_queue_item(
            db=db,
            job_instruction=item.job_instruction,
            opps_id=item.opps_id,
            tenant_id=item.tenant_id,
            request_type=item.request_type,
            job_submitted_by=item.job_submitted_by,
            next_state=item.next_state
        )
        
        if not new_item:
            raise HTTPException(status_code=500, detail="Failed to create proposal queue item")
        
        return {
            "message": "Proposal queue item created successfully",
            "job_id": new_item.job_id,
            "id": new_item.id
        }
    except Exception as e:
        logger.error(f"Error creating proposal queue item: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/queue/proposal/{job_id}/status")
async def update_proposal_queue_status(
    job_id: str,
    status_update: QueueStatusUpdate,
    db: AsyncSession = Depends(get_customer_db)
):
    """Update proposal queue status"""
    try:
        success = await ProposalQueueService.update_proposal_queue_status(
            db=db,
            job_id=job_id,
            status=status_update.status,
            next_state=status_update.next_state
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Proposal queue item not found")
        
        return {"message": f"Status updated to {status_update.status}"}
    except Exception as e:
        logger.error(f"Error updating proposal queue status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Custom Opps Queue endpoints
@app.get("/queue/custom-opps/new")
async def get_new_custom_opps_queue_items(
    limit: int = 10,
    db: AsyncSession = Depends(get_customer_db)
):
    """Get new custom opps queue items"""
    try:
        items = await CustomOppsQueueService.get_custom_opps_queue_items(db, limit)
        return {
            "items": [
                {
                    "id": item.id,
                    "opps_source": item.opps_source,
                    "opps_id": item.opps_id,
                    "status": item.status,
                    "tenant_id": item.tenant_id,
                    "created_date": item.created_date.isoformat() if item.created_date else None,
                    "update_date": item.update_date.isoformat() if item.update_date else None
                }
                for item in items
            ],
            "count": len(items)
        }
    except Exception as e:
        logger.error(f"Error getting new custom opps queue items: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/queue/custom-opps")
async def create_custom_opps_queue_item(
    item: CustomOppsQueueCreate,
    db: AsyncSession = Depends(get_customer_db)
):
    """Create a new custom opps queue item"""
    try:
        new_item = await CustomOppsQueueService.create_custom_opps_queue_item(
            db=db,
            opps_source=item.opps_source,
            opps_id=item.opps_id,
            tenant_id=item.tenant_id,
            originating_ip_address=item.originating_ip_address
        )
        
        if not new_item:
            raise HTTPException(status_code=500, detail="Failed to create custom opps queue item")
        
        return {
            "message": "Custom opps queue item created successfully",
            "opps_id": new_item.opps_id,
            "id": new_item.id
        }
    except Exception as e:
        logger.error(f"Error creating custom opps queue item: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/queue/custom-opps/{opps_id}/status")
async def update_custom_opps_queue_status(
    opps_id: str,
    status_update: QueueStatusUpdate,
    db: AsyncSession = Depends(get_customer_db)
):
    """Update custom opps queue status"""
    try:
        success = await CustomOppsQueueService.update_custom_opps_queue_status(
            db=db,
            opps_id=opps_id,
            status=status_update.status
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Custom opps queue item not found")
        
        return {"message": f"Status updated to {status_update.status}"}
    except Exception as e:
        logger.error(f"Error updating custom opps queue status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
category_task = None

@app.post("/category/generate")
async def generate_category_descriptions():
    global category_task
    if category_task and not category_task.done():
        return {"message": "Category generation already running."}
    category_task = asyncio.create_task(category_main())
    return {"message": "Category generation started in background."}

@app.post("/category/stop")
async def stop_category_generation():
    global category_task
    if category_task and not category_task.done():
        category_task.cancel()
        return {"message": "Category generation cancelled."}
    return {"message": "No running category generation task."}


# RFP Draft Export endpoint
@app.get("/export/rfp-draft")
async def export_rfp_draft_to_pdf(
    opportunity_id: str = Query(..., description="The opportunity ID"),
    tenant_id: str = Query(..., description="The tenant ID"),
    user_id: int = Query(..., description="The user ID"), #69
    version: int = Query(..., description="The version number"), #1
    cover_page: Optional[int] = Query(None, description="Cover page ID"), #4195
    trailing_page: Optional[int] = Query(None, description="Trailing page ID"),
    volume_number: int = Query(1, description="Volume number"), #1
    db: AsyncSession = Depends(get_customer_db)
):
    """Export RFP draft to PDF (currently generates markdown file)"""
    try:
        # Export RFP draft
        file_path, success_message = await RfpDraftExportController.export_rfp_draft(
            db=db,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            user_id=user_id,
            version=version,
            cover_page_id=cover_page,
            trailing_page_id=trailing_page,
            volume_number=volume_number
        )
        
        return {
            "message": success_message,
            "file_path": file_path,
            "opportunity_id": opportunity_id,
            "tenant_id": tenant_id,
            "version": version
        }
        
    except Exception as e:
        logger.error(f"Error exporting RFP draft: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Run the application
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.debug,
        log_level="info"
    ) 