import json
from datetime import datetime, date

from database import Base
from sqlalchemy import (BigInteger, Boolean, Column, DateTime, Float, Integer, String, Text, LargeBinary)
from pgvector.sqlalchemy import Vector 


def _json_serialize(value):
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    if isinstance(value, bytes):
        return value.decode('utf-8', errors='replace')
    if isinstance(value, (list, tuple)):
        return [_json_serialize(v) for v in value]
    if isinstance(value, dict):
        return {k: _json_serialize(v) for k, v in value.items()}
    return value

class BaseModelMixin:
    def as_dict(self):
        result = {}
        table = getattr(self, "__table__", None)
        if table is not None:
            for column in table.columns:
                value = getattr(self, column.name)
                result[column.name] = _json_serialize(value)
        return result

# Opportunity Schema Models
class CustomOpportunityTableInfo(Base, BaseModelMixin):
    __tablename__ = "custom_oppstableinfo"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    opps_id = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    status = Column(String(255), nullable=False)
    opps_raw_text = Column(Text)
    created_date = Column(DateTime, nullable=False)

    
class ProposalQueue(Base, BaseModelMixin):
    __tablename__ = "proposal_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    job_id = Column(String(255), nullable=False)
    job_instruction = Column(Text, nullable=False)
    creation_date = Column(DateTime, default=datetime.utcnow)
    job_submitted_by = Column(String(255))
    status = Column(String(255), nullable=False)
    next_state = Column(Text)
    opps_id = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    request_type = Column(Integer, nullable=False)


class CustomOppsQueue(Base, BaseModelMixin):
    __tablename__ = "custom_opps_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    opps_source = Column(String(255))
    opps_id = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow)
    update_date = Column(DateTime)
    status = Column(String(255))
    tenant_id = Column(String(255))
    originating_ip_address = Column(String)


class CustomOppsTable(Base, BaseModelMixin):
    __tablename__ = "custom_oppstable"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    posted_date = Column(DateTime)
    expiration_date = Column(DateTime)
    naics_code = Column(String(255))
    opportunity_id = Column(String(255))
    opportunity_type = Column(String(255))
    classification_code = Column(String(255))
    point_of_contact_first_name = Column(String(255))
    point_of_contact_last_name = Column(String(255))
    point_of_contact_email = Column(String(255))
    point_of_contact_phone = Column(String(255))
    place_of_performance_city = Column(String(255))
    place_of_performance_state = Column(String(255))
    place_of_performance_zip = Column(String(255))
    place_of_performance_country = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow)
    description_text = Column(Text)
    agency_code = Column(String(255))
    last_mod_date = Column(DateTime)
    sub_agency_code = Column(String(255))
    summary_text = Column(Text)
    tenant_id = Column(String(255))
    grading_criteria_text = Column(Text)
    created_by = Column(String)
    toc_text = Column(Text)
    toc_text_2 = Column(Text)
    toc_text_3 = Column(Text)
    toc_text_4 = Column(Text)
    toc_text_5 = Column(Text)
    requirement_text = Column(Text)
    format_compliance = Column(Text)
    structure_compliance = Column(Text)
    content_compliance = Column(Text)
    cover_page_fields = Column(Text)
    proposal_outline_1 = Column(Text)
    proposal_outline_2 = Column(Text)
    proposal_outline_3 = Column(Text)
    proposal_outline_4 = Column(Text)
    proposal_outline_5 = Column(Text)
    keywords = Column(Text)
    draft = Column(Text)


class Users(Base, BaseModelMixin):
    __tablename__ = "users"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(255))
    email = Column(String(255))
    email_verified = Column(DateTime)
    password = Column(String(255), nullable=False)
    client = Column(String(255), nullable=False)
    image = Column(String(255))
    last_login = Column(DateTime)
    rating_preference = Column(Float)
    tenant_id = Column(String(255))
    group_id = Column(Integer, default=1, nullable=False)
    is_active = Column(Boolean, default=False, nullable=False)
    subscription_id = Column(String(255))
    license = Column(LargeBinary)  # bytea in PostgreSQL


class AESTenant(Base, BaseModelMixin):
    __tablename__ = "aes_tenant"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    tenant_id = Column(String(255), unique=True, nullable=False)
    tenant_name = Column(String(255), nullable=False)
    tenant_primary_contact_firstname = Column(String(255))
    tenant_primary_contact_lastname = Column(String(255))
    tenant_primary_contact_email = Column(String(255))
    tenant_primary_address_1 = Column(String(255))
    tenant_primary_address_2 = Column(String(255))
    tenant_primary_city = Column(String(255))
    tenant_primary_state = Column(String(255))
    tenant_primary_country = Column(String(255))
    tenant_primary_zipcode = Column(String(255))
    tenant_domain = Column(String(255), nullable=False)
    duns_number = Column(String)
    cage_code = Column(String)
    sam_entity_id = Column(String)
    is_super_domain = Column(Boolean, default=False, nullable=False)
    tenant_lic = Column(LargeBinary)  # bytea in PostgreSQL
    encryption_key = Column(String)
    rating_preference = Column(Float)
    autogenerate_proposal = Column(Boolean)
    notification_method = Column(String(255))
    has_completed_onboarding = Column(Boolean, default=False, nullable=False)
    subscription_id = Column(String(255))
    trial_end_date = Column(DateTime)
    naics_codes_updated_at = Column(DateTime)
    agencies_updated_at = Column(DateTime)
    use_internet = Column(Boolean)


# New tables to be added

class ProposalEncryption(Base, BaseModelMixin):
    __tablename__ = "proposal_encryption"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    tenant_id = Column(String(255), nullable=False)
    public_key = Column(LargeBinary, nullable=False)  # bytea in PostgreSQL
    encrypted_private_key = Column(LargeBinary, nullable=False)  # bytea in PostgreSQL
    salt = Column(LargeBinary, nullable=False)  # bytea in PostgreSQL
    passphrase_hash = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)


class DataMetastore(Base, BaseModelMixin):
    __tablename__ = "datametastore"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    record_identifier = Column(String(255), nullable=False)
    record_type = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    original_document_file_name = Column(String(255))
    original_document_content_type = Column(String(255), nullable=False)
    created_date = Column(DateTime, nullable=False)
    original_document = Column(LargeBinary)  # bytea in PostgreSQL
    raw_text_document = Column(LargeBinary)  # bytea in PostgreSQL
    owner = Column(String(255))


class DataMetastoreQueue(Base, BaseModelMixin):
    __tablename__ = "datametastore_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    job_id = Column(String(255), nullable=False)
    job_instruction = Column(Text, nullable=False)
    creation_date = Column(DateTime)
    job_submitted_by = Column(String(255))
    status = Column(String(255), nullable=False)
    tenant_id = Column(String(255))
    last_updated_date = Column(DateTime)
    original_document_file_name = Column(String(255))
    source = Column(String(255), default="LOCAL")

class DatametastoreImages(Base, BaseModelMixin):
    __tablename__ = 'datametastore_images'
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(String(255),  nullable=False)
    raw_image = Column(LargeBinary)
    description = Column(Text)
    tenant_id = Column(String(255))
    record_type = Column(String(255))
    created_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated_date = Column(DateTime, default=datetime.utcnow)
    last_processed_date = Column(DateTime)

class NotificationQueue(Base, BaseModelMixin):
    __tablename__ = "notification_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    tenant_id = Column(String(255), nullable=False)
    job_info = Column(String(255))
    status = Column(String(255), nullable=False)
    title = Column(String(255))
    message = Column(String(255))
    created_at = Column(DateTime)
    sent_at = Column(DateTime)


class ProposalsInReview(Base, BaseModelMixin):
    __tablename__ = "proposals_in_review"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    client_short_name = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    section_number = Column(String(255), nullable=False)
    opps_id = Column(String(255), nullable=False)
    created_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_mod_date = Column(DateTime, nullable=False)
    proposal_data = Column(LargeBinary)  # bytea in PostgreSQL
    version = Column(Integer, nullable=False)
    due_date = Column(DateTime)
    status = Column(String(255))
    opps_type = Column(String(255))
    finalized = Column(String(255))
    volume_number = Column(Integer)
    job_instruction = Column(Text)


class ProposalsFormatQueue(Base, BaseModelMixin):
    __tablename__ = "proposals_format_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    client_short_name = Column(String(255), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    opps_id = Column(String(255), nullable=False)
    created_date = Column(DateTime, nullable=False)
    version = Column(Integer, nullable=False)
    status = Column(String(255))
    format_type = Column(Integer, nullable=False)
    format_details = Column(String(255), nullable=False)
    proposal_data = Column(LargeBinary)  # bytea in PostgreSQL
    job_submitted_by = Column(String(255))
    is_rfp = Column(Boolean)
    cover_page = Column(BigInteger)
    trailing_page = Column(BigInteger)
    opp_source = Column(String)


class Simulations(Base, BaseModelMixin):
    __tablename__ = "simulations"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    title = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    tenant_id = Column(String(255), nullable=False)
    job_id = Column(String(255), nullable=False)
    simulation_result = Column(Text)
    simulation_summary = Column(Text)


class SimulationQueue(Base, BaseModelMixin):
    __tablename__ = "simulation_queue"
    __table_args__ = {"schema": "opportunity"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    status = Column(String(255))
    job_instruction = Column(Text, nullable=False)
    job_submitted_by = Column(BigInteger, nullable=False)
    job_id = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.astimezone, nullable=False) 

class ClientProcessQueue(Base, BaseModelMixin):
    __tablename__ = "client_process_queue"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    status = Column(String(255))
    job_instruction = Column(Text, nullable=False)
    job_submitted_by = Column(BigInteger, nullable=False)
    job_id = Column(String(255), nullable=False)
    creation_date = Column(DateTime, default=datetime.astimezone, nullable=False) 
    last_updated_date = Column(DateTime, default=datetime.astimezone, nullable=False)


class Category(Base, BaseModelMixin):
    __tablename__ = "category"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    category_name = Column(String(255))
    category_type = Column(String(255))
    category_description = Column(Text)
    embedding = Column(Vector)


class ClientProfile(Base, BaseModelMixin):
    __tablename__ = "oppclientversions"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    client_short_name = Column(String(255))
    profile_id = Column(String(255))
    tenant_id = Column(String(255))
    title = Column(String(255))
    is_default = Column(Boolean, default=False)
    


class OppClientInfo(Base, BaseModelMixin):
    __tablename__ = "oppclientinfo"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    client_short_name = Column(String(255))
    client_raw_text = Column(Text)
    created_date = Column(DateTime)
    tenant_id = Column(String(255))
    embeddings = Column(Vector)
    updated_date = Column(DateTime)
    profile_id = Column(String(255))

class SystemPromptParameters(Base, BaseModelMixin):
    __tablename__ = "system_prompt_parameters"
    __table_args__ = {"schema": "opportunity"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(255))
    description = Column(Text)
    tone_settings = Column(Text)
    structural_preferences = Column(Text)
    audience_profile = Column(Text)
    style_guidance = Column(Text)
    