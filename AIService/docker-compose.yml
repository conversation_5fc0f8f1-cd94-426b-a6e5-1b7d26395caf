services:
  aiservice:
    build:
      context: .
      dockerfile: Dockerfile
    image: kontratar/aiservice:${TAG:-latest}
    container_name: aiservice-app
    ports:
      - "${APP_PORT}:${APP_PORT}"
    environment:
      - APP_HOST=${APP_HOST}
      - APP_PORT=${APP_PORT}
      - DEBUG=${DEBUG}
      # Database Configuration - Kontratar Database
      - KONTRATAR_DB_HOST=${KONTRATAR_DB_HOST}
      - KONTRATAR_DB_PORT=${KONTRATAR_DB_PORT}
      - KONTRATAR_DB_NAME=${KONTRATAR_DB_NAME}
      - KONTRATAR_DB_USER=${KON<PERSON>ATAR_DB_USER}
      - KONTRATAR_DB_PASSWORD=${KONTRATAR_DB_PASSWORD}
      # Database Configuration - Customer Database
      - CUSTOMER_DB_HOST=${CUSTOMER_DB_HOST}
      - CUSTOMER_DB_PORT=${CUSTOMER_DB_PORT}
      - CUSTOMER_DB_NAME=${CUSTOMER_DB_NAME}
      - CUSTOMER_DB_USER=${CUSTOMER_DB_USER}
      - CUSTOMER_DB_PASSWORD=${CUSTOMER_DB_PASSWORD}
      # LangChain Configuration
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      # ChromaDB Configuration
      - CHROMADB_PORT_1=${CHROMADB_PORT_1}
      - CHROMADB_PORT_2=${CHROMADB_PORT_2}
      - CHROMADB_PORT_3=${CHROMADB_PORT_3}
      - CHROMADB_PORT_4=${CHROMADB_PORT_4}
      - CHROMADB_PORT_5=${CHROMADB_PORT_5}
      - CHROMADB_PROTOCOL=${CHROMADB_PROTOCOL}
      - CHROMADB_SERVER_NAME=${CHROMADB_SERVER_NAME}
    networks:
      - aiservice-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://${APP_HOST}:${APP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  aiservice-network:
    driver: bridge
