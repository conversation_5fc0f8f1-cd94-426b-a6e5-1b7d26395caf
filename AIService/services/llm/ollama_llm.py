
from langchain_ollama import ChatOllama

class OllamaLLMService:
     """
     Servce for using the Ollama LLM wrapper
     """
     def __init__(self):
          self.llm_api_url: str = "http://ai.kontratar.com:11434",
          self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url="http://ai.kontratar.com:11434")
          self.image_system_prompt = """
          You are an expert at describing images in detail. Provide a concise but comprehensive description of the image.
          """
       
     
     async def prompt(self, user_prompt: str, img_bytes: bytes = None) -> str:
         """
         Generate a prompt response using the Ollama LLM.
         
         Args:
             system_prompt (str): The system prompt defining the LLM's role and context.
             user_prompt (str): The user's input prompt.
             img_bytes (bytes, optional): Base64 encoded image bytes for image analysis. Defaults to None.
         
         Returns:
             str: The generated response from the LLM.
         """
         try:
             # Prepare messages for the LLM
             messages = [
                 {"role": "system", "content": self.image_system_prompt},
                 {"role": "user", "content": user_prompt}
             ]
             
             # If image is provided, convert to base64 and append to user prompt
             if img_bytes:
                 import base64
                 img_base64 = base64.b64encode(img_bytes).decode('utf-8')
                 messages[-1]["content"] += f"\n[Image: {img_base64}]"
             
             # Invoke the LLM and return the generated content
             response = await self.llm.ainvoke(messages)
             return response.content
         
         except Exception as e:
             # Log or handle any errors during LLM invocation
             raise RuntimeError(f"Error generating prompt response: {e}")