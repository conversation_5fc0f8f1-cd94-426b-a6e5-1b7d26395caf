from typing import Any, Dict, List

from loguru import logger

from .custom_opps_scheduler_service import CustomOppsSchedulerService
from .proposal_scheduler_service import ProposalSchedulerService
from .sam_opps_scheduler_service import SamOppsSchedulerService
from .client_process_queue_scheduler_service import ClientProcessQueueSchedulerService
from .datametastore_queue_scheduler_service import DataMetastoreQueueSchedulerService
from .datametastore_scheduler_service import DataMetastoreSchedulerService
from .simulation_scheduler_service import SimulationSchedulerService

class SchedulerManager:
    """Manager for controlling all schedulers including proposal, custom opps, sam opps, client process, and datametastore queue schedulers"""
    
    def __init__(self):
        self.proposal_scheduler = ProposalSchedulerService()
        self.custom_opps_scheduler = CustomOppsSchedulerService()
        self.sam_opps_scheduler = SamOppsSchedulerService()
        self.client_schedule = ClientProcessQueueSchedulerService()
        self.datametastore_queue_scheduler = DataMetastoreQueueSchedulerService()
        self.datametastore_scheduler = DataMetastoreSchedulerService()
        self.simulation_scheduler = SimulationSchedulerService()
    
    def start_all(self, interval_seconds: int = 30):
        """Start all schedulers"""
        logger.info("Starting all schedulers...")
        self.proposal_scheduler.start(interval_seconds)
        self.custom_opps_scheduler.start(interval_seconds)
        self.sam_opps_scheduler.start(interval_seconds)
        self.client_schedule.start(interval_seconds)
        self.datametastore_queue_scheduler.start(interval_seconds)
        self.simulation_scheduler.start(interval_seconds)
        logger.info("All schedulers started")
    
    def start_doc_service(self, interval_seconds: int = 90):
        """Start document scheduler"""
        logger.info("Starting document schedulers...")
        self.datametastore_scheduler.start(interval_seconds)
        logger.info("Document started")    
        
    def stop_all(self):
        """Stop all schedulers"""
        logger.info("Stopping all schedulers...")
        self.proposal_scheduler.stop()
        self.custom_opps_scheduler.stop()
        self.sam_opps_scheduler.stop()
        self.client_schedule.stop()
        self.datametastore_queue_scheduler.stop()
        self.simulation_scheduler.stop()
        logger.info("All schedulers stopped")
    
    def restart_all(self, interval_seconds: int = 30):
        """Restart all schedulers"""
        logger.info("Restarting all schedulers...")
        self.stop_all()
        self.start_all(interval_seconds)
        logger.info("All schedulers restarted")
    
    def enable_all(self):
        """Enable all schedulers"""
        logger.info("Enabling all schedulers...")
        self.proposal_scheduler.enable()
        self.custom_opps_scheduler.enable()
        self.sam_opps_scheduler.enable()
        self.client_schedule.enable()
        self.datametastore_queue_scheduler.enable()
        self.simulation_scheduler.enable()
        logger.info("All schedulers enabled")
    
    def disable_all(self):
        """Disable all schedulers"""
        logger.info("Disabling all schedulers...")
        self.proposal_scheduler.disable()
        self.custom_opps_scheduler.disable()
        self.sam_opps_scheduler.disable()
        self.client_schedule.disable()
        self.datametastore_queue_scheduler.disable()
        self.simulation_scheduler.disable()
        logger.info("All schedulers disabled")
    
    def enable_proposal_scheduler(self):
        """Enable only the proposal scheduler"""
        self.proposal_scheduler.enable()
    
    def disable_proposal_scheduler(self):
        """Disable only the proposal scheduler"""
        self.proposal_scheduler.disable()
    
    def enable_custom_opps_scheduler(self):
        """Enable only the custom opps scheduler"""
        self.custom_opps_scheduler.enable()
    
    def disable_custom_opps_scheduler(self):
        """Disable only the custom opps scheduler"""
        self.custom_opps_scheduler.disable()
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all schedulers"""
        return {
            "proposal_scheduler": self.proposal_scheduler.get_status(),
            "custom_opps_scheduler": self.custom_opps_scheduler.get_status(),
            "sam_opps_scheduler": self.sam_opps_scheduler.get_status(),
            "client_schedule": self.client_schedule.get_status(),
            "datametastore_queue_scheduler": self.datametastore_queue_scheduler.get_status(),
            "all_running": (self.proposal_scheduler.is_running and 
                          self.custom_opps_scheduler.is_running and 
                          self.sam_opps_scheduler.is_running and 
                          self.client_schedule.is_running and
                          self.datametastore_queue_scheduler.is_running),
            "all_enabled": (self.proposal_scheduler.is_scheduler_enabled() and 
                          self.custom_opps_scheduler.is_scheduler_enabled() and 
                          self.sam_opps_scheduler.is_scheduler_enabled() and 
                          self.client_schedule.is_scheduler_enabled() and
                          self.datametastore_queue_scheduler.is_scheduler_enabled())
        }
    
    def get_proposal_scheduler_status(self) -> Dict[str, Any]:
        """Get proposal scheduler status"""
        return self.proposal_scheduler.get_status()
    
    def get_custom_opps_scheduler_status(self) -> Dict[str, Any]:
        """Get custom opps scheduler status"""
        return self.custom_opps_scheduler.get_status()
    
    def get_datametastore_queue_scheduler_status(self) -> Dict[str, Any]:
        """Get datametastore queue scheduler status"""
        return self.datametastore_queue_scheduler.get_status()

    def get_simulation_scheduler_status(self):
        return self.simulation_scheduler.get_status()
    
    @property
    def is_running(self) -> bool:
        """Check if any scheduler is running"""
        return (
            self.proposal_scheduler.is_running or
            self.custom_opps_scheduler.is_running or
            self.sam_opps_scheduler.is_running or
            self.client_schedule.is_running or
            self.datametastore_queue_scheduler.is_running
        )
    
    @property
    def is_enabled(self) -> bool:
        """Check if any scheduler is enabled"""
        return (
            self.proposal_scheduler.is_scheduler_enabled() or
            self.custom_opps_scheduler.is_scheduler_enabled() or
            self.sam_opps_scheduler.is_scheduler_enabled() or
            self.client_schedule.is_scheduler_enabled() or
            self.datametastore_queue_scheduler.is_scheduler_enabled()
        ) 