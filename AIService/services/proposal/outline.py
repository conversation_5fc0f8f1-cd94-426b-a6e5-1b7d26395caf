import re
import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from services.chroma.chroma_service import ChromaService

from database import get_customer_db, get_kontratar_db
from langchain_ollama import ChatOllama
from services.proposal.utilities import ProposalUtilities
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from loguru import logger

# Import validation components
try:
    from services.proposal.validation.enhanced_draft_generator import EnhancedDraftGenerator
    from services.proposal.validation.validation_logger import validation_logger
    VALIDATION_AVAILABLE = True
    logger.info("Validation system loaded successfully")
except ImportError as e:
    logger.warning(f"Validation components not available: {e}")
    VALIDATION_AVAILABLE = False
    EnhancedDraftGenerator = None
    validation_logger = None

def remove_first_markdown_title_regex(text: str) -> str:
    """
    Remove only the first line that starts with ## using regex.
    
    Args:
        text: Input text containing markdown titles
        
    Returns:
        Text with only the first ## title removed
    """
    # Remove only the first line starting with ## (with optional whitespace before ##)
    return re.sub(r'^\s*##.*$', '', text, flags=re.MULTILINE, count=1).strip()


class ProposalOutlineService:
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(
            model="gemma3:27b", 
            num_ctx=6300, 
            temperature=0, 
            base_url=llm_api_url
        )

    def generate_chroma_query(self, text: str, is_rfp: bool = True):
        if not text:
            return ""

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        if is_rfp:
            prompt = (
                "Given the following RFP volume information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, evaluation criteria, and any details relevant to the specific volume described below. "
                "This is for a single volume of a multi-volume RFP.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFI information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, government needs, and any details relevant to the RFI topics described below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        response = llm.invoke(prompt)
        return str(response.content)

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_main_info_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record        

    async def generate_table_of_contents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        volume_information: str,
        content_compliance: str,
        is_rfp: bool
    ) -> Dict[str, Any]:
        '''
        chroma_query = self.generate_chroma_query(volume_information, is_rfp)

        async for db in get_kontratar_db():
            max_chunks = 1
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            try:
                relevant_chunks = await asyncio.wait_for(
                    self.chroma_service.get_relevant_chunks(db, collection_name, chroma_query, n_results=max_chunks),
                    timeout=30.0  # 30 second timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"ChromaDB timeout for collection {collection_name} - using empty context")
                relevant_chunks = []
            except Exception as e:
                logger.error(f"Error retrieving chunks: {e} - using empty context")
                relevant_chunks = []
            # Clean up newlines and tabs
            toc_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context = "\n".join(toc_context)
            break
        '''

        system_prompt = '''
            **Task:**
            Your task is to generate a table of contents for an RFI or Volume of an RFP.
            You will be given different background information to help you generate this table of contents:
            1. Information on how the RFI or Volume should be structured as well as the terminologies/naming convention to use, 
            this will be found in <structure-compliance>
            2. Information on the content that must been to be seen in the RFI or Volume, this will be found in <content-compliance>
            3. Related information from the vector database, this will be found in <context>
        
            **Important:**
            1. YOU MUST only generate the table of contents for the RFI or RFP Volume passed in <structure-compliance>
            2. If you are building the table of contents for an RFP Volume, only use information relevant information found in <content-compliance>
            3. YOU MUST ADD 3 experiences UNDER DEMONSTRATED EXPERIENCE or PAST PERFORMANCE.
            4. YOU MUST ADD ALL Statement of Work Tasks to the TECHNICAL APPROACH (or similar) with the exact naming. DO NOT leave out ANYTHING from the SOW.
            5. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            6. You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''

        user_prompt = f'''
            Generate the table of contents for this RFI/RFP
            
            <structure-compliance>
                {volume_information}
            </structure-compliance>

            <content-compliance>
                {content_compliance}
            </content-compliance>

            USE ONLY information found in <structure-compliance>, <content-compliance>, and <context> to build the response.

            Use the JSON schema below:
            {{
                "table_of_contents": [
                    {{
                        "title": "string",
                        "description": "string",
                        "number": "string",
                        "subsections": [
                            {{
                                "number": "string",
                                "title": "string",
                                "description": "string"
                            }}
                        ]
                    }}
                ]
            }}

            - "table_of_contents" is an array of sections for the RFI or RFP Volume.
            - "title" is the name of the main section or subsection AND it MUST align with the naming conventions used in <structure-compliance>
            - "description" MUST be a brief description of the content that must be contained in that section or subsection.
            - "subsections" is an array of subsections for each section.
            - "number" is THE assigned number for a main section or subsection (eg main section 1.0, subsection 1.1.3). "number" MUST
                start from 1.0
        '''

        # LLM call (with retry logic)
        content = None
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        content = self.llm.invoke(messages)

        print(content.content)
        
        return {"content": content.content}

    
    async def generate_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a detailed outline for each section and subsection in the table of contents.
        Each outline contains a title, content, and optionally an array of image descriptions.
        The outlines are nested to match the table of contents hierarchy.

        Enhanced for government standards with:
        - Comprehensive validation and error handling
        - Government-compliant prompt engineering
        - Structured context retrieval
        - Quality assurance checks
        """

        logger.info(f"OUTLINE: Starting enhanced outline generation for opportunity {opportunity_id}")
        logger.info(f"OUTLINE: Processing {len(table_of_contents)} main sections")

        @retry(
            stop=stop_after_attempt(5),
            wait=wait_fixed(3),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def outline_for_section(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            """Enhanced section outline generation with government compliance"""
            section_title = section.get("title", "").strip()
            section_desc = section.get("description", "").strip()
            section_number = section.get("number", "").strip()

            indent = "  " * depth
            logger.info(f"OUTLINE: {indent}Processing section {section_number} - {section_title}")

            if not section_title:
                logger.warning(f"OUTLINE: {indent}Empty section title found, skipping")
                return {}

            # Enhanced ChromaDB query strategy
            chroma_queries = [
                f"Requirements and evaluation criteria for {section_title} section",
                f"Content specifications and deliverables for {section_title}",
                f"Government standards and compliance requirements for {section_title}",
                f"Technical approach and methodology requirements for {section_title}"
            ]

            # Fetch comprehensive context with timeout protection
            context_chunks = []
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    for query in chroma_queries:
                        try:
                            chunks = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, collection_name, query, n_results=2),
                                timeout=30.0
                            )
                            context_chunks.extend(chunks)
                        except asyncio.TimeoutError:
                            logger.warning(f"OUTLINE: {indent}ChromaDB timeout for query: {query[:50]}...")
                        except Exception as e:
                            logger.error(f"OUTLINE: {indent}ChromaDB error for query: {e}")
                    break

            except Exception as e:
                logger.error(f"OUTLINE: {indent}Database connection error: {e}")

            # Process and clean context
            if context_chunks:
                section_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in context_chunks]
                context = "\n".join(section_context)
                logger.info(f"OUTLINE: {indent}Retrieved {len(context_chunks)} context chunks ({len(context)} chars)")
            else:
                context = f"Section: {section_title}\nDescription: {section_desc}"
                logger.warning(f"OUTLINE: {indent}No context retrieved, using fallback")

            # Enhanced government-compliant system prompt
            system_prompt = '''
                **ROLE:** Government Proposal Outline Expert
                **MISSION:** Generate comprehensive, government-compliant proposal section outlines that meet federal evaluation standards.

                **CRITICAL GOVERNMENT COMPLIANCE REQUIREMENTS:**
                1. ZERO placeholders, brackets, TBD, TODO, or incomplete information in any field
                2. NO generic content - everything must be specific to THIS section and requirement
                3. NO repetition of RFP administrative requirements or formatting rules
                4. FOCUS EXCLUSIVELY on demonstrating technical capability for the specific requirement
                5. PROVIDE concrete, actionable guidance that leads to substantive content
                6. ENSURE all guidance aligns with federal proposal evaluation criteria
                7. MANDATE specific methodologies, processes, and measurable outcomes

                **OUTLINE COMPONENTS (ALL REQUIRED):**
                - title: Exact section title from table of contents
                - content: Comprehensive guidance on what to include and what to avoid
                - page_limit: Maximum pages allowed (extract from context or use standard: 2-5 pages)
                - purpose: Primary evaluation purpose (Demonstrate Capability, Show Understanding, Prove Experience, etc.)
                - rfp_vector_db_query: Specific query to retrieve RFP requirements for this section
                - client_vector_db_query: Targeted query to get relevant company capabilities
                - custom_prompt: Detailed, step-by-step content generation instructions
                - references: Exact text from context that supports this outline
                - image_descriptions: Required tables/diagrams (if applicable)

                **CONTENT GUIDANCE STANDARDS:**
                - Specify EXACTLY what technical details to include
                - Define SPECIFIC methodologies and processes to describe
                - Identify MEASURABLE outcomes and success criteria to present
                - Clarify HOW to demonstrate understanding of government requirements
                - Outline CONCRETE examples and case studies to reference
                - Specify COMPLIANCE requirements and standards to address

                **CUSTOM PROMPT REQUIREMENTS:**
                - Include step-by-step content creation instructions
                - Specify required technical depth and detail level
                - Mandate specific government terminology and naming conventions
                - Define required structure (headers, bullets, tables)
                - Include quality checkpoints and validation criteria
                - Specify word count and page limit compliance

                **MANDATORY TABLE/DIAGRAM IDENTIFICATION:**
                - Staffing Plan: Role/Responsibilities/Qualifications table
                - Technical Approach: Process flow diagrams, methodology tables
                - Past Performance: Project summary tables
                - Management Plan: Organizational charts, timeline tables
                - Quality Assurance: QA process diagrams, metrics tables

                **JSON COMPLIANCE:**
                - Return ONLY valid JSON - no explanatory text
                - Follow the exact schema provided
                - Ensure all string fields are properly escaped
                - Validate all required fields are present
            '''

            # Enhanced user prompt with structured context
            user_prompt = f'''
                **SECTION ANALYSIS:**
                Section Number: {section_number}
                Section Title: {section_title}
                Section Description: {section_desc}

                **RFP CONTEXT:**
                {context[:2000] if context else "No specific RFP context available"}

                **REQUIREMENTS:**
                Generate a comprehensive outline that enables creation of government-compliant content.
                Focus on the specific requirements and evaluation criteria for this section.

                **MANDATORY JSON SCHEMA:**
                {{
                    "title": "string - exact section title",
                    "content": "string - detailed guidance on what to include/exclude (minimum 200 words)",
                    "page_limit": number - maximum pages (2-5 typical),
                    "purpose": "string - primary evaluation purpose",
                    "rfp_vector_db_query": "string - specific query for RFP requirements",
                    "client_vector_db_query": "string - targeted query for company capabilities",
                    "custom_prompt": "string - step-by-step content generation instructions (minimum 300 words)",
                    "references": "string - exact text from context supporting this outline",
                    "image_descriptions": ["string"] - required tables/diagrams (if applicable)
                }}

                **VALIDATION REQUIREMENTS:**
                - All fields must be complete and substantive
                - No placeholders or generic content
                - Custom prompt must include specific government terminology
                - Content guidance must be actionable and specific
                - References must be exact quotes from provided context
            '''

            # Enhanced LLM call with comprehensive validation
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"OUTLINE: {indent}LLM attempt {attempt + 1}/{max_attempts} for {section_title}")

                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                    result = self.llm.invoke(messages)
                    content = str(result.content).strip()

                    if not content:
                        logger.warning(f"OUTLINE: {indent}Empty LLM response on attempt {attempt + 1}")
                        continue

                    # Enhanced JSON extraction with validation
                    outline = ProposalUtilities.extract_json_from_brackets(content)

                    if outline is None:
                        logger.warning(f"OUTLINE: {indent}Failed to extract JSON on attempt {attempt + 1}")
                        logger.debug(f"OUTLINE: {indent}Raw content: {content[:200]}...")
                        continue

                    # Validate required fields and content quality
                    validation_errors = self._validate_outline_quality(outline, section_title, indent)

                    if validation_errors:
                        logger.warning(f"OUTLINE: {indent}Validation errors on attempt {attempt + 1}: {validation_errors}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            # Use outline with warnings for final attempt
                            logger.warning(f"OUTLINE: {indent}Using outline with validation warnings")

                    logger.info(f"OUTLINE: {indent}Successfully generated outline for {section_title}")

                    # Recursively process subsections with depth tracking
                    subsections = section.get("subsections", [])
                    if subsections:
                        outline["subsections"] = []
                        logger.info(f"OUTLINE: {indent}Processing {len(subsections)} subsections")
                        for subsection in subsections:
                            sub_outline = await outline_for_section(subsection, depth + 1)
                            if sub_outline:  # Only add non-empty subsections
                                outline["subsections"].append(sub_outline)

                    return outline

                except Exception as e:
                    logger.error(f"OUTLINE: {indent}Error on attempt {attempt + 1}: {e}")
                    if attempt == max_attempts - 1:
                        raise
                    continue

            # Fallback if all attempts failed
            logger.error(f"OUTLINE: {indent}All attempts failed for {section_title}")
            return {}

        # Build the enhanced nested outline structure with comprehensive tracking
        outlines = []
        total_sections = len(table_of_contents)
        successful_sections = 0

        for i, section in enumerate(table_of_contents, 1):
            logger.info(f"OUTLINE: Processing main section {i}/{total_sections}: {section.get('title', 'Unknown')}")

            try:
                outline = await outline_for_section(section, depth=0)
                if outline:
                    outlines.append(outline)
                    successful_sections += 1
                    logger.info(f"OUTLINE: Successfully processed section {i}/{total_sections}")
                else:
                    logger.warning(f"OUTLINE: Empty outline returned for section {i}/{total_sections}")
            except Exception as e:
                logger.error(f"OUTLINE: Failed to process section {i}/{total_sections}: {e}")
                # Continue with other sections rather than failing completely
                continue

        # Generate comprehensive summary
        logger.info(f"OUTLINE: Generation complete - {successful_sections}/{total_sections} sections successful")

        return {
            "outlines": outlines,
            "generation_summary": {
                "total_sections": total_sections,
                "successful_sections": successful_sections,
                "success_rate": (successful_sections / total_sections * 100) if total_sections > 0 else 0,
                "enhanced_features": [
                    "Government compliance validation",
                    "Multi-query context retrieval",
                    "Comprehensive error handling",
                    "Quality assurance checks"
                ]
            }
        }

    def _validate_outline_quality(self, outline: Dict[str, Any], section_title: str, indent: str = "") -> List[str]:
        """Validate outline quality against government standards"""
        errors = []

        # Required fields validation
        required_fields = ["title", "content", "page_limit", "purpose", "rfp_vector_db_query",
                          "client_vector_db_query", "custom_prompt", "references"]

        for field in required_fields:
            if field not in outline:
                errors.append(f"Missing required field: {field}")
            elif not outline[field] or (isinstance(outline[field], str) and len(outline[field].strip()) < 10):
                errors.append(f"Field '{field}' is empty or too short")

        # Content quality validation
        if "content" in outline:
            content = outline["content"]
            if len(content) < 200:
                errors.append("Content guidance too short (minimum 200 characters)")
            if any(placeholder in content.lower() for placeholder in ["[", "]", "tbd", "todo", "placeholder"]):
                errors.append("Content contains placeholders or brackets")

        # Custom prompt validation
        if "custom_prompt" in outline:
            prompt = outline["custom_prompt"]
            if len(prompt) < 300:
                errors.append("Custom prompt too short (minimum 300 characters)")
            if "step" not in prompt.lower():
                errors.append("Custom prompt missing step-by-step instructions")

        # Page limit validation
        if "page_limit" in outline:
            try:
                page_limit = int(outline["page_limit"])
                if page_limit < 1 or page_limit > 20:
                    errors.append("Page limit should be between 1-20 pages")
            except (ValueError, TypeError):
                errors.append("Page limit must be a valid number")

        if errors:
            logger.warning(f"OUTLINE: {indent}Quality validation failed for {section_title}: {errors}")

        return errors


    ## This is to generate a draft
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft for each section and subsection in the table of contents.
        The drafts are nested to match the table of contents hierarchy.
        """

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        @retry(
            stop=stop_after_attempt(5),  # Retry up to 3 times
            wait=wait_fixed(3),          # Wait 1 second between retries
            retry=retry_if_exception_type(Exception),  # Retry on any Exception
            reraise=True                 # Reraise the last exception if all retries fail
        )
        async def draft_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            # Compose a query for this section
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Section title: {section_title}, Section Description: {section_desc}")
            
            # Fetch relevant context for this section
            
            chroma_query = f"""
                Return all the content needed to be seen in proposal section titled '{section_title}'.
            """

            client_query = f"""
                Return information relevant to a proposal section titled '{section_title}'
            """

            async for db in get_kontratar_db():
                opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                opportunity_chunks = await self.chroma_service.get_relevant_chunks(
                    db, opportunity_collection, chroma_query, n_results=3
                )
                rfp_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in opportunity_chunks]
                context = "\n".join(rfp_context)
                #logger.info(f"Context: {section_context}")

                client_collection = f"{tenant_id}_{client_short_name}"
                relevant_chunks = await self.chroma_service.get_relevant_chunks(
                    db, client_collection, client_query, n_results=3
                )
                client_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                client_context_str = "\n".join(client_context)

                break
            

            system_prompt = '''
                **Task:**
                Generate professional government proposal content with 100% compliance.

                **CRITICAL REQUIREMENTS:**
                1. NO placeholders, brackets, TBD, TODO, or incomplete information
                2. NO contact info in technical sections (except cover letters)
                3. NO repetition of RFP requirements or administrative details
                4. NO generic marketing language - be specific and evidence-based
                5. FOCUS ONLY on demonstrating capability for the requested work
                6. Use concrete methodologies and measurable outcomes
                7. Write for government evaluators assessing technical capability
                8. COMPLY with page limits

                **CONTENT STANDARDS:**
                - Demonstrate HOW you will perform the work with specific processes
                - Show understanding of government requirements
                - Provide specific methodologies, tools, and success criteria
                - Focus on capability demonstration, not company marketing
                - Use professional, direct language
                - Structure content for easy evaluation (headers, bullets, tables)
                - Include specific metrics, timelines, and deliverables
                - Keep paragraphs concise (3-5 sentences maximum)

                **FORMATTING STANDARDS:**
                - Use clear section headers and subheaders
                - Include bullet points for key information
                - Generate relevant tables in markdown when they support capability demonstration
                - Tables must be complete with specific data - NO placeholders
                - Use active voice and strong action verbs
                - Ensure content is scannable and easy to evaluate
            '''

            
            user_prompt = f'''
                Write a {section_title} section for a government proposal.

                **REQUIREMENTS:**
                - Return ONLY the section content - NO titles or explanations
                - NO placeholders, brackets, TBD, TODO, or incomplete information
                - NO contact info, generic marketing, or RFP repetition
                - Demonstrate HOW you will perform the work with specific processes
                - Use concrete methodologies and measurable outcomes
                - Comply with page limits
                - Use clear structure with headers and bullet points where appropriate

                **RFP CONTEXT:**
                {context[:1500] if context else "No context provided"}

                **COMPANY INFORMATION:**
                {tenant_metadata[:800] if tenant_metadata else ""}
                {client_context_str[:800] if 'client_context_str' in locals() and client_context_str else ""}

                Generate professional content demonstrating specific technical capability.
            '''

            # LLM call with retry logic
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            content = str(result.content)
            
            text = remove_first_markdown_title_regex(content)

            text = text.strip()
            if text.startswith('```'):
                first_newline = text.find('\n')
                if first_newline != -1:
                    text = text[first_newline + 1:]

            if text.endswith('```'):
                text = text[:-3]
            text = text.replace('```', '')
            text = text.strip()

            draft = { 
                "title": section_number + " " + section_title, 
                "content": text, 
                "number": section_number 
            }
            
            print(f"Raw content: {text}")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                draft["subsections"] = []
                for subsection in subsections:
                    sub_outline = await draft_for_section(subsection)
                    draft["subsections"].append(sub_outline)

            return draft

        # Build the nested outline structure
        drafts = []
        for section in table_of_contents:
            outline = await draft_for_section(section)
            drafts.append(outline)

        return { "draft": drafts }

    ## Enhanced draft generation with validation
    async def generate_validated_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft with integrated validation
        """

        if not VALIDATION_AVAILABLE:
            logger.warning("Validation system not available, falling back to standard draft generation")
            return await self.generate_draft(
                opportunity_id, tenant_id, source, table_of_contents, tenant_metadata, client_short_name
            )

        logger.info(f"Starting enhanced draft generation with AUTO-FIXING validation for opportunity {opportunity_id}")

        # Initialize validation session
        session_id = validation_logger.start_validation_session(
            opportunity_id, tenant_id, len(table_of_contents)
        )

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        # Use the new enhanced generator with auto-fixing
        from services.proposal.enhanced_draft_generator import EnhancedDraftGenerator
        enhanced_generator = EnhancedDraftGenerator()

        @retry(
            stop=stop_after_attempt(3),
            wait=wait_fixed(2),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def validated_draft_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Generating validated draft for section: {section_number} - {section_title}")

            # Fetch relevant context for this section
            chroma_query = f"Return all content needed for proposal section titled '{section_title}'"
            client_query = f"Return information relevant to proposal section titled '{section_title}'"

            # Initialize fallback contexts
            rfp_context = f"Section: {section_title}\nDescription: {section_desc}"
            company_context = "Professional services company with expertise in government contracting."

            try:
                async for db in get_kontratar_db():
                    opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    # Try to get opportunity chunks with timeout
                    try:
                        logger.info(f"Fetching RFP context for {section_title} from {opportunity_collection}")
                        opportunity_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, opportunity_collection, chroma_query, n_results=5),
                            timeout=45.0
                        )
                        rfp_context = "\n".join([chunk.replace("\n", " ").replace("\t", " ") for chunk in opportunity_chunks])
                        logger.info(f"Retrieved RFP context ({len(rfp_context)} chars) for {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for opportunity collection {opportunity_collection} - using fallback")
                    except Exception as e:
                        logger.error(f"Error retrieving opportunity chunks: {e} - using fallback")

                    # Try to get client chunks with timeout
                    client_collection = f"{tenant_id}_{client_short_name}"
                    try:
                        logger.info(f"Fetching company context for {section_title} from {client_collection}")
                        relevant_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, client_collection, client_query, n_results=3),
                            timeout=45.0
                        )
                        company_context = "\n".join([chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks])
                        logger.info(f"Retrieved company context ({len(company_context)} chars) for {section_title}")
                    except asyncio.TimeoutError:
                        logger.warning(f"ChromaDB timeout for client collection {client_collection} - using fallback")
                    except Exception as e:
                        logger.error(f"Error retrieving client chunks: {e} - using fallback")

                    break

            except Exception as e:
                logger.error(f"Database connection error: {e} - using minimal fallback context")

            # Add tenant metadata to company context
            full_company_context = f"{tenant_metadata}\n\n{company_context}"

            # Generate validated draft using enhanced generator
            draft_result = await enhanced_generator.generate_validated_draft(
                section_title=section_title,
                section_desc=section_desc,
                section_number=section_number,
                rfp_context=rfp_context,
                company_context=full_company_context,
                max_retries=3
            )

            logger.info(f"Generated validated draft for {section_number} - Validation Score: {draft_result['validation_score']:.1f}%")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                draft_result["subsections"] = []
                for subsection in subsections:
                    sub_draft = await validated_draft_for_section(subsection)
                    draft_result["subsections"].append(sub_draft)

            return draft_result

        # Generate AUTO-FIXED validated proposal using enhanced generator
        try:
            # Get opportunity context
            opportunity_context = str(record) if record else ""

            # Generate complete validated proposal with auto-fixing
            proposal_result = await enhanced_generator.generate_validated_proposal(
                table_of_contents=table_of_contents,
                opportunity_context=opportunity_context,
                company_context="",
                tenant_metadata=tenant_metadata
            )

            # Convert to expected format
            drafts = []
            for section_result in proposal_result["sections"]:
                draft_section = {
                    "title": section_result["title"],
                    "description": section_result["description"],
                    "content": section_result["content"],
                    "validation_passed": section_result["validation_passed"],
                    "issues_found": section_result["issues_found"],
                    "fixes_applied": section_result["fixes_applied"]
                }
                drafts.append(draft_section)

            # Create enhanced validation summary
            validation_summary = {
                "overall_valid": proposal_result["validation_summary"]["passed"],
                "validation_score": proposal_result["quality_score"],
                "valid_sections": proposal_result["perfect_sections"],
                "total_sections": proposal_result["total_sections"],
                "total_issues": proposal_result["total_issues_found"],
                "total_fixes_applied": proposal_result["total_fixes_applied"],
                "quality_grade": "A" if proposal_result["quality_score"] >= 90 else "B" if proposal_result["quality_score"] >= 80 else "C"
            }

            validation_logger.complete_validation_session(validation_summary)

            logger.info(f"ENHANCED: AUTO-FIX VALIDATION SUMMARY:")
            logger.info(f"   Overall Valid: {validation_summary['overall_valid']}")
            logger.info(f"   Quality Score: {validation_summary['validation_score']:.1f}%")
            logger.info(f"   Quality Grade: {validation_summary['quality_grade']}")
            logger.info(f"   Perfect Sections: {validation_summary['valid_sections']}/{validation_summary['total_sections']}")
            logger.info(f"   Total Issues Found: {validation_summary['total_issues']}")
            logger.info(f"   Total Fixes Applied: {validation_summary['total_fixes_applied']}")

            return {
                "draft": drafts,
                "validation_summary": validation_summary,
                "session_id": session_id,
                "auto_fixing_enabled": True,
                "quality_score": proposal_result["quality_score"]
            }

        except Exception as e:
            validation_logger.log_critical_error(f"Failed to generate validated draft: {str(e)}", e)
            raise

    

